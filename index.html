<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>慧习作家长端小程序 - 网页版</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      background-color: #f5f5f5;
      padding: 20px;
      max-width: 800px;
      margin: 0 auto;
    }
    
    h1 {
      font-size: 24px;
      margin-bottom: 20px;
      color: #333;
      text-align: center;
    }
    
    h2 {
      font-size: 20px;
      margin: 20px 0 10px;
      color: #444;
    }
    
    p {
      margin-bottom: 15px;
    }
    
    .card {
      background-color: white;
      border-radius: 10px;
      padding: 20px;
      margin-bottom: 20px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
    
    .btn {
      display: inline-block;
      background-color: #4A90E2;
      color: white;
      padding: 10px 20px;
      border-radius: 5px;
      text-decoration: none;
      font-weight: bold;
      margin-top: 10px;
      text-align: center;
    }
    
    .btn:hover {
      background-color: #3A80D2;
    }
    
    .page-list {
      list-style: none;
      margin: 15px 0;
    }
    
    .page-list li {
      margin-bottom: 10px;
      padding: 10px;
      background-color: #f9f9f9;
      border-radius: 5px;
      border-left: 4px solid #4A90E2;
    }
    
    .page-list a {
      color: #4A90E2;
      text-decoration: none;
      font-weight: bold;
    }
    
    .page-list a:hover {
      text-decoration: underline;
    }
    
    .note {
      background-color: #FFF8E1;
      padding: 15px;
      border-radius: 5px;
      border-left: 4px solid #FFC107;
      margin: 20px 0;
    }
    
    @media (max-width: 600px) {
      body {
        padding: 15px;
      }
      
      h1 {
        font-size: 22px;
      }
      
      .card {
        padding: 15px;
      }
    }
  </style>
</head>
<body>
  <div class="card">
    <h1>慧习作家长端小程序 - 网页版</h1>
    <p>本项目是基于Figma设计稿转换的慧习作家长端小程序的网页版实现。使用纯HTML、CSS和JavaScript开发，无需任何框架依赖。</p>
    
    <h2>已实现页面</h2>
    <ul class="page-list">
      <li>
        <a href="pages/personal-center/index.html">个人中心</a> - 显示用户个人信息、家庭成员、设置选项和底部导航栏
      </li>
    </ul>
    
    <div class="note">
      <p><strong>注意：</strong>目前仅实现了个人中心页面，其他页面尚未开发。点击页面中的链接和按钮会在控制台输出导航信息，但不会实际跳转。</p>
    </div>
    
    <a href="pages/personal-center/index.html" class="btn">查看个人中心页面</a>
  </div>
  
  <div class="card">
    <h2>项目说明</h2>
    <p>本项目基于Figma设计稿「慧习作_家长端小程序_AI开发版」，使用纯HTML、CSS和JavaScript实现，无需任何框架依赖。</p>
    <p>项目结构和详细说明请参考 <a href="README.md">README.md</a> 文件。</p>
  </div>
</body>
</html>