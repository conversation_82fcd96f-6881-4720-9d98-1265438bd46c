/**
 * 个人中心页面脚本
 */

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    // 初始化页面元素和事件
    initPage();
});

/**
 * 初始化页面
 */
function initPage() {
    // 添加设置项点击事件
    initSettingItems();
    
    // 添加底部导航点击事件
    initBottomNav();
    
    // 添加家庭成员点击事件
    initFamilyMembers();
    
    // 添加个人信息编辑点击事件
    initProfileEdit();
}

/**
 * 初始化设置项点击事件
 */
function initSettingItems() {
    const settingItems = document.querySelectorAll('.setting-item');
    
    settingItems.forEach(item => {
        item.addEventListener('click', function() {
            const settingText = this.querySelector('.setting-text').textContent;
            
            switch(settingText) {
                case '消息中心':
                    // 跳转到消息中心页面
                    console.log('跳转到消息中心页面');
                    // window.location.href = '../message-center/index.html';
                    break;
                    
                case '意见反馈':
                    // 跳转到意见反馈页面
                    console.log('跳转到意见反馈页面');
                    // window.location.href = '../feedback/index.html';
                    break;
                    
                case '切换身份':
                    // 跳转到切换身份页面
                    console.log('跳转到切换身份页面');
                    // window.location.href = '../switch-identity/index.html';
                    break;
                    
                case '退出登录':
                    // 退出登录操作
                    handleLogout();
                    break;
                    
                default:
                    break;
            }
        });
    });
}

/**
 * 初始化底部导航点击事件
 */
function initBottomNav() {
    const navItems = document.querySelectorAll('.nav-item');
    
    navItems.forEach((item, index) => {
        item.addEventListener('click', function() {
            const navText = this.querySelector('.nav-text').textContent;
            
            switch(navText) {
                case '首页':
                    // 跳转到首页
                    console.log('跳转到首页');
                    // window.location.href = '../home/<USER>';
                    break;
                    
                case '过往作文':
                    // 跳转到过往作文页面
                    console.log('跳转到过往作文页面');
                    // window.location.href = '../essays/index.html';
                    break;
                    
                case '成长数据':
                    // 跳转到成长数据页面
                    console.log('跳转到成长数据页面');
                    // window.location.href = '../growth/index.html';
                    break;
                    
                case '个人中心':
                    // 当前页面，不做跳转
                    console.log('当前已在个人中心页面');
                    break;
                    
                default:
                    break;
            }
        });
    });
}

/**
 * 初始化家庭成员点击事件
 */
function initFamilyMembers() {
    const familyAvatars = document.querySelectorAll('.family-avatar');
    const addMember = document.querySelector('.add-member');
    
    // 为每个家庭成员头像添加点击事件
    familyAvatars.forEach((avatar, index) => {
        avatar.addEventListener('click', function() {
            // 跳转到对应家庭成员详情页面
            console.log(`查看家庭成员${index + 1}详情`);
            // window.location.href = `../family-member/index.html?id=${index + 1}`;
        });
    });
    
    // 为添加成员按钮添加点击事件
    if (addMember) {
        addMember.addEventListener('click', function() {
            // 跳转到添加家庭成员页面
            console.log('添加家庭成员');
            // window.location.href = '../add-family-member/index.html';
        });
    }
}

/**
 * 初始化个人信息编辑点击事件
 */
function initProfileEdit() {
    const profileEdit = document.querySelector('.profile-edit');
    const editIcon = document.querySelector('.edit-icon');
    
    // 为编辑个人信息文本添加点击事件
    if (profileEdit) {
        profileEdit.addEventListener('click', function() {
            // 跳转到编辑个人信息页面
            console.log('编辑个人信息');
            // window.location.href = '../edit-profile/index.html';
        });
    }
    
    // 为编辑图标添加点击事件
    if (editIcon) {
        editIcon.addEventListener('click', function() {
            // 跳转到编辑个人信息页面
            console.log('编辑个人信息');
            // window.location.href = '../edit-profile/index.html';
        });
    }
}

/**
 * 处理退出登录
 */
function handleLogout() {
    if (confirm('确定要退出登录吗？')) {
        // 清除登录状态
        console.log('退出登录');
        // 清除本地存储的用户信息
        // localStorage.removeItem('userInfo');
        // 跳转到登录页面
        // window.location.href = '../login/index.html';
    }
}