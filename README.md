# 慧习作家长端小程序 - 网页版

本项目是基于Figma设计稿转换的慧习作家长端小程序的网页版实现。使用纯HTML、CSS和JavaScript开发，无需任何框架依赖。

## 项目结构

```
project/
├── pages/                  # 页面目录
│   └── personal-center/    # 个人中心页面
│       ├── index.html      # 页面HTML
│       ├── styles.css      # 页面样式
│       └── script.js       # 页面脚本
├── assets/                 # 资源目录
│   ├── images/             # 图片资源
│   ├── icons/              # 图标文件
│   └── fonts/              # 字体文件
├── common/                 # 公共代码目录
│   ├── components/         # 公共组件
│   ├── css/                # 公共样式
│   │   ├── reset.css       # 样式重置
│   │   └── common.css      # 通用样式
│   └── js/                 # 公共脚本
│       └── utils.js        # 工具函数
├── site-map.json           # 网站结构和页面关系映射
└── README.md               # 项目说明文档
```

## 页面说明

### 个人中心页面

- **路径**: `/pages/personal-center/index.html`
- **功能**: 显示用户个人信息、家庭成员、设置选项和底部导航栏
- **交互**: 
  - 点击底部导航可切换到不同页面
  - 点击设置项可进入对应功能页面
  - 点击家庭成员头像可查看成员详情
  - 点击添加成员可添加新的家庭成员
  - 点击编辑个人信息可修改用户资料

## 页面关系

网站的页面关系和导航结构记录在 `site-map.json` 文件中，包含以下信息：

- 所有页面的基本信息（ID、名称、路径等）
- 页面间的跳转关系
- 导航元素和对应的目标页面

## 使用说明

1. 直接在浏览器中打开 `pages/personal-center/index.html` 文件即可查看个人中心页面
2. 页面中的链接和按钮目前仅在控制台输出日志，未实际跳转到其他页面
3. 要完成完整的网站功能，需要根据设计稿继续开发其他页面

## 开发说明

### 添加新页面

1. 在 `pages` 目录下创建新的页面目录
2. 创建 `index.html`、`styles.css` 和 `script.js` 文件
3. 参考现有页面的结构和样式进行开发
4. 更新 `site-map.json` 文件，添加新页面的信息和导航关系

### 样式规范

- 使用 `reset.css` 重置浏览器默认样式
- 使用 `common.css` 定义通用样式和组件
- 每个页面有自己的 `styles.css` 文件，定义页面特有的样式
- 使用语义化的HTML标签和类名

### JavaScript规范

- 使用 `utils.js` 中的工具函数进行通用操作
- 每个页面有自己的 `script.js` 文件，处理页面特有的交互逻辑
- 使用模块化的方式组织代码，避免全局变量污染

## 注意事项

- 本项目使用纯HTML、CSS和JavaScript开发，无需任何框架依赖
- 页面设计基于移动端尺寸，适合在手机或平板上查看
- 图片资源需要从Figma设计稿中导出并放置在 `assets/images` 目录下
- 字体文件需要下载并放置在 `assets/fonts` 目录下