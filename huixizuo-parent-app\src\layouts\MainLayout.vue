<template>
  <div class="main-layout">
    <!-- 顶部导航栏 -->
    <el-header class="main-header" height="60px">
      <div class="header-content">
        <!-- 左侧Logo和标题 -->
        <div class="header-left">
          <div class="logo">
            <img src="/logo.svg" alt="慧习作" class="logo-image" />
            <span class="app-name">慧习作</span>
          </div>
        </div>
        
        <!-- 右侧用户信息 -->
        <div class="header-right">
          <!-- 消息通知 -->
          <el-badge :value="notificationCount" :hidden="notificationCount === 0" class="notification-badge">
            <el-button type="text" class="notification-btn">
              <el-icon size="20"><Bell /></el-icon>
            </el-button>
          </el-badge>
          
          <!-- 用户菜单 -->
          <el-dropdown @command="handleUserMenuCommand" class="user-dropdown">
            <div class="user-info">
              <el-avatar :src="authStore.userAvatar" :size="32" />
              <span class="user-name">{{ authStore.userName }}</span>
              <el-icon class="dropdown-icon"><ArrowDown /></el-icon>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">
                  <el-icon><User /></el-icon>
                  个人中心
                </el-dropdown-item>
                <el-dropdown-item command="settings">
                  <el-icon><Setting /></el-icon>
                  设置
                </el-dropdown-item>
                <el-dropdown-item divided command="logout">
                  <el-icon><SwitchButton /></el-icon>
                  退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </div>
    </el-header>
    
    <!-- 主要内容区域 -->
    <el-container class="main-container">
      <!-- 侧边栏（桌面端显示） -->
      <el-aside class="main-aside" width="240px" v-if="!isMobile">
        <div class="sidebar-content">
          <el-menu
            :default-active="currentRoute"
            class="sidebar-menu"
            router
            :collapse="false"
          >
            <el-menu-item index="/home">
              <el-icon><House /></el-icon>
              <span>首页</span>
            </el-menu-item>
            <el-menu-item index="/homework">
              <el-icon><Document /></el-icon>
              <span>作业管理</span>
              <el-badge :value="pendingHomeworkCount" :hidden="pendingHomeworkCount === 0" class="menu-badge" />
            </el-menu-item>
            <el-menu-item index="/report">
              <el-icon><DataAnalysis /></el-icon>
              <span>学习报告</span>
            </el-menu-item>
            <el-menu-item index="/learning-analysis">
              <el-icon><PieChart /></el-icon>
              <span>学习分析</span>
            </el-menu-item>
            <el-menu-item index="/ai-tutor">
              <el-icon><ChatDotRound /></el-icon>
              <span>AI辅导</span>
            </el-menu-item>
            <el-menu-item index="/child-management">
              <el-icon><UserFilled /></el-icon>
              <span>孩子管理</span>
            </el-menu-item>
          </el-menu>
        </div>
      </el-aside>
      
      <!-- 主内容区 -->
      <el-main class="main-content">
        <transition name="page" mode="out-in">
          <router-view />
        </transition>
      </el-main>
    </el-container>
    
    <!-- 底部导航栏（移动端显示） -->
    <div class="bottom-navigation" v-if="isMobile">
      <div class="nav-item" 
           v-for="item in bottomNavItems" 
           :key="item.path"
           :class="{ active: currentRoute === item.path }"
           @click="$router.push(item.path)"
      >
        <div class="nav-icon">
          <el-icon :size="20">
            <component :is="item.icon" />
          </el-icon>
          <el-badge 
            v-if="item.badge && item.badgeCount > 0" 
            :value="item.badgeCount" 
            class="nav-badge"
          />
        </div>
        <span class="nav-label">{{ item.label }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'
import { ElMessageBox } from 'element-plus'
import {
  Bell,
  ArrowDown,
  User,
  Setting,
  SwitchButton,
  House,
  Document,
  DataAnalysis,
  PieChart,
  ChatDotRound,
  UserFilled
} from '@element-plus/icons-vue'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()

// 响应式状态
const isMobile = ref(false)
const notificationCount = ref(3) // 模拟通知数量
const pendingHomeworkCount = ref(5) // 模拟待完成作业数量

// 当前路由
const currentRoute = computed(() => route.path)

// 底部导航项
const bottomNavItems = [
  {
    path: '/home',
    label: '首页',
    icon: 'House',
    badge: false
  },
  {
    path: '/homework',
    label: '作业',
    icon: 'Document',
    badge: true,
    badgeCount: pendingHomeworkCount.value
  },
  {
    path: '/report',
    label: '报告',
    icon: 'DataAnalysis',
    badge: false
  },
  {
    path: '/profile',
    label: '我的',
    icon: 'User',
    badge: false
  }
]

// 检查是否为移动端
const checkMobile = () => {
  isMobile.value = window.innerWidth <= 768
}

// 处理用户菜单命令
const handleUserMenuCommand = async (command) => {
  switch (command) {
    case 'profile':
      router.push('/profile')
      break
    case 'settings':
      router.push('/settings')
      break
    case 'logout':
      try {
        await ElMessageBox.confirm(
          '确定要退出登录吗？',
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
        authStore.logout()
        router.push('/auth/login')
      } catch {
        // 用户取消
      }
      break
  }
}

onMounted(() => {
  checkMobile()
  window.addEventListener('resize', checkMobile)
})

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile)
})
</script>

<style scoped>
.main-layout {
  min-height: 100vh;
  background-color: var(--app-bg-color);
}

.main-header {
  background: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 0;
  z-index: 1000;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  padding: 0 24px;
}

.header-left {
  display: flex;
  align-items: center;
}

.logo {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-image {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  background: var(--primary-color);
}

.app-name {
  font-size: 18px;
  font-weight: 600;
  color: var(--text-primary);
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.notification-badge {
  margin-right: 8px;
}

.notification-btn {
  padding: 8px;
  border-radius: 50%;
}

.user-dropdown {
  cursor: pointer;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 4px 8px;
  border-radius: 20px;
  transition: background-color 0.2s;
}

.user-info:hover {
  background-color: var(--border-color-extra-light);
}

.user-name {
  font-size: 14px;
  color: var(--text-primary);
  font-weight: 500;
}

.dropdown-icon {
  font-size: 12px;
  color: var(--text-secondary);
}

.main-container {
  height: calc(100vh - 60px);
}

.main-aside {
  background: white;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.sidebar-content {
  height: 100%;
  padding: 16px 0;
}

.sidebar-menu {
  border: none;
  height: 100%;
}

.sidebar-menu .el-menu-item {
  height: 48px;
  line-height: 48px;
  margin: 4px 16px;
  border-radius: 8px;
  position: relative;
}

.sidebar-menu .el-menu-item.is-active {
  background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
  color: white;
}

.menu-badge {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
}

.main-content {
  padding: 0;
  overflow-y: auto;
  background-color: var(--app-bg-color);
}

.bottom-navigation {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 60px;
  background: white;
  border-top: 1px solid var(--border-color-light);
  display: flex;
  align-items: center;
  justify-content: space-around;
  z-index: 1000;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4px 8px;
  cursor: pointer;
  transition: color 0.2s;
  position: relative;
  min-width: 60px;
}

.nav-item.active {
  color: var(--primary-color);
}

.nav-icon {
  position: relative;
  margin-bottom: 2px;
}

.nav-badge {
  position: absolute;
  top: -8px;
  right: -8px;
}

.nav-label {
  font-size: 10px;
  line-height: 1;
}

/* 页面过渡动画 */
.page-enter-active,
.page-leave-active {
  transition: all 0.3s ease;
}

.page-enter-from {
  opacity: 0;
  transform: translateX(20px);
}

.page-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    padding: 0 16px;
  }
  
  .app-name {
    font-size: 16px;
  }
  
  .user-name {
    display: none;
  }
  
  .main-content {
    padding-bottom: 60px;
  }
}

@media (max-width: 480px) {
  .header-content {
    padding: 0 12px;
  }
  
  .logo {
    gap: 8px;
  }
  
  .logo-image {
    width: 28px;
    height: 28px;
  }
  
  .app-name {
    font-size: 14px;
  }
}
</style>
