/* 个人中心页面样式 */

/* 容器样式 */
.container {
    width: 750px;
    height: 1688px;
    overflow: hidden;
    background-color: #FFFFFF;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
}

/* 顶部状态栏 */
.status-bar {
    width: 750px;
    height: 80px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
}

.status-left {
    width: 144px;
    height: 80px;
    overflow: hidden;
    display: flex;
    justify-content: flex-end;
    align-items: flex-end;
}

.signal-icons {
    margin-bottom: 25.02px;
    margin-right: 29.32px;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 54.68px;
    height: 21.40px;
}

.signal-icons {
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    width: 54.68px;
    height: 21.40px;
}

.signal-icon:nth-child(1) {
    width: 15.72px;
    height: 21.40px;
}

.signal-icon:nth-child(2) {
    width: 4.50px;
    height: 14.94px;
    margin-top: 3.22px;
    margin-left: 3.48px;
}

.signal-icon:nth-child(3) {
    width: 16.20px;
    height: 20.38px;
    margin-top: 0.50px;
    margin-left: 3.38px;
}

.signal-icon:nth-child(4) {
    width: 8.96px;
    height: 20.38px;
    margin-top: 0.50px;
    margin-left: 2.44px;
}

.status-right {
    width: 192px;
    height: 80px;
    overflow: hidden;
    margin-left: 414px;
    display: flex;
    justify-content: flex-start;
    align-items: flex-end;
}

.battery-icons {
    margin-left: 24px;
    margin-bottom: 25.54px;
    display: flex;
    justify-content: center;
    align-items: center;
    width: 131.74px;
    height: 22.12px;
}

.battery-icons {
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
    width: 131.74px;
    height: 22.12px;
}

.battery-icon:nth-child(1) {
    width: 5.10px;
    height: 6.80px;
    margin-top: 15.32px;
}

.battery-icon:nth-child(2) {
    width: 5.10px;
    height: 11.06px;
    margin-top: 11.06px;
    margin-left: 3.40px;
}

.battery-icon:nth-child(3) {
    width: 5.10px;
    height: 17.02px;
    margin-top: 5.10px;
    margin-left: 3.40px;
}

.battery-icon:nth-child(4) {
    width: 5.10px;
    height: 20.42px;
    margin-top: 1.70px;
    margin-left: 3.40px;
}

.battery-icon:nth-child(5) {
    width: 28.94px;
    height: 20.14px;
    margin-top: 1.70px;
    margin-left: 13.62px;
}

.battery-percentage {
    margin-left: 11.92px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-end;
    width: 42.56px;
    height: 22.12px;
    background-image: url('../../assets/images/battery-bg.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
}

.battery-percent-img {
    width: 35.74px;
    height: 15.32px;
    margin-right: 3.40px;
}

.battery-dot {
    width: 2.38px;
    height: 7.18px;
    margin-top: 8.52px;
    margin-left: 1.70px;
}

/* 个人信息区域 */
.profile-section {
    width: 360px;
    height: 160px;
    margin-top: 56px;
    margin-left: 42px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
}

.avatar {
    width: 160px;
    height: 160px;
    overflow: hidden;
    border-radius: 80px;
}

.profile-info {
    width: 144px;
    height: 140px;
    margin-left: 56px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
}

.profile-name {
    width: 144px;
    height: 52px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
}

.profile-name span {
    font-family: 'Inter', sans-serif;
    font-size: 32px;
    white-space: nowrap;
    color: #171A1F;
    line-height: 52px;
    font-weight: normal;
}

.edit-icon {
    width: 24px;
    height: 24px;
    margin-left: 18px;
}

.profile-edit {
    font-family: 'Inter', sans-serif;
    font-size: 16px;
    white-space: nowrap;
    color: #BCC1CA;
    line-height: 28px;
    font-weight: normal;
}

.profile-badge {
    width: 78px;
    height: 36px;
    margin-top: 24px;
}

/* 家庭成员区域 */
.family-section {
    margin-top: 56px;
    margin-left: 40px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    width: 670px;
    height: 312px;
    background-image: url('../../assets/images/family-bg.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
}

.family-title {
    margin-top: 20px;
    margin-left: 32px;
    font-family: 'Inter', sans-serif;
    font-size: 32px;
    white-space: nowrap;
    color: #171A1F;
    line-height: 52px;
    font-weight: normal;
}

.family-members {
    margin-top: 36px;
    margin-left: 56px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    width: 588px;
    height: 156px;
}

.family-avatars {
    width: 588px;
    height: 96px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
}

.family-avatar {
    width: 88px;
    height: 88px;
    overflow: hidden;
    border-radius: 44px;
}

.family-avatar:not(:first-child) {
    margin-left: 36px;
}

.add-member {
    width: 96px;
    height: 96px;
    border-radius: 44px;
    box-shadow: 0px 0px 2px 0px rgba(23,26,31,0.07), 0px 0px 4px 0px rgba(23,26,31,0.12);
    margin-left: 32px;
}

.family-names {
    width: 560px;
    height: 44px;
    margin-top: 16px;
    margin-left: 10px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
}

.family-name {
    font-family: 'Inter', sans-serif;
    font-size: 28px;
    white-space: nowrap;
    text-align: center;
    color: #323842;
    line-height: 44px;
    font-weight: normal;
}

.family-name:nth-child(1) {
    margin-left: 0;
}

.family-name:nth-child(2) {
    margin-left: 52px;
}

.family-name:nth-child(3) {
    margin-left: 50px;
}

.family-name:nth-child(4) {
    margin-left: 58px;
}

.family-name:nth-child(5) {
    margin-left: 70px;
}

/* 设置选项区域 */
.settings-section {
    margin-top: 56px;
    margin-left: 40px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    width: 670px;
    height: 336px;
    background-image: url('../../assets/images/settings-bg.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
}

.setting-item {
    width: 668px;
    height: 74px;
    border-radius: 12px;
    border: 2px solid rgba(0,0,0,0);
    background-color: #F8F9FA;
    display: flex;
    justify-content: flex-start;
    align-items: flex-start;
}

.settings-section .setting-item:first-child {
    margin-top: 16px;
}

.settings-section .setting-item:not(:first-child) {
    margin-top: 2px;
}

.setting-text {
    margin-top: 16px;
    margin-left: 40px;
    font-family: 'Inter', sans-serif;
    font-size: 28px;
    white-space: nowrap;
    color: #171A1F;
    line-height: 44px;
    font-weight: normal;
}

.setting-arrow {
    width: 32px;
    height: 32px;
    margin-top: 22px;
    margin-left: 446px;
}

.version-text {
    margin-top: 16px;
    margin-left: 446px;
    font-family: 'Inter', sans-serif;
    font-size: 28px;
    white-space: nowrap;
    color: #171A1F;
    line-height: 44px;
    font-weight: normal;
}

/* 退出登录区域 */
.logout-section {
    margin-top: 28px;
    margin-left: 40px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    width: 670px;
    height: 108px;
    background-image: url('../../assets/images/logout-bg.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
}

.logout-section .setting-item {
    margin-top: 16px;
}

/* 分隔线 */
.divider {
    width: 750px;
    height: 8px;
    margin-top: 360px;
}

/* 底部导航栏 */
.bottom-nav {
    width: 726.08px;
    height: 96px;
    background-color: rgba(0, 0, 0, 0);
    margin-top: 16px;
    margin-left: 12px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
}

.nav-item {
    width: 181.52px;
    height: 96px;
    overflow: hidden;
    background-color: rgba(0, 0, 0, 0);
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
}

.nav-icon-container {
    width: 48px;
    height: 50.40px;
    margin-top: 5.60px;
    margin-left: 66.76px;
    position: relative;
}

.nav-icon {
    width: 48px;
    height: 48px;
    position: absolute;
    top: 2.40px;
    left: 0;
}

.notification-badge {
    width: 24px;
    border-radius: 10px;
    border: 4px solid #FFFFFF;
    background-color: #DE3B40;
    min-height: 24px;
    position: absolute;
    top: 0;
    left: 24px;
}

.nav-text {
    margin-left: 70.76px;
    font-family: 'Inter', sans-serif;
    font-size: 20px;
    white-space: nowrap;
    color: #565E6C;
    line-height: 32px;
    font-weight: normal;
}

.nav-item:not(:first-child) .nav-icon {
    margin-top: 8px;
    margin-left: 66.76px;
    position: static;
}

.nav-item:nth-child(2) .nav-text {
    margin-left: 50.76px;
}

.nav-item:nth-child(3) .nav-text {
    margin-left: 50.76px;
    color: #424355;
}

.nav-item:nth-child(4) .nav-text {
    margin-left: 50.76px;
    color: #636AE8;
    font-weight: bold;
}

.nav-text.active {
    color: #636AE8;
    font-weight: bold;
}