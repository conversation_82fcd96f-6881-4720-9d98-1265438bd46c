{"version": 3, "file": "QueueAction.js", "sourceRoot": "", "sources": ["../../../../src/internal/scheduler/QueueAction.ts"], "names": [], "mappings": ";AAAA,OAAO,EAAE,WAAW,EAAE,MAAM,eAAe,CAAC;AAM5C;IAAoC,+BAAc;IAChD,qBAAsB,SAAyB,EAAY,IAAmD;QAA9G,YACE,kBAAM,SAAS,EAAE,IAAI,CAAC,SACvB;QAFqB,eAAS,GAAT,SAAS,CAAgB;QAAY,UAAI,GAAJ,IAAI,CAA+C;;IAE9G,CAAC;IAEM,8BAAQ,GAAf,UAAgB,KAAS,EAAE,KAAiB;QAAjB,sBAAA,EAAA,SAAiB;QAC1C,IAAI,KAAK,GAAG,CAAC,EAAE;YACb,OAAO,iBAAM,QAAQ,YAAC,KAAK,EAAE,KAAK,CAAC,CAAC;SACrC;QACD,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;QACnB,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAC3B,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,6BAAO,GAAd,UAAe,KAAQ,EAAE,KAAa;QACpC,OAAO,KAAK,GAAG,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC,CAAC,iBAAM,OAAO,YAAC,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;IAC9F,CAAC;IAES,oCAAc,GAAxB,UAAyB,SAAyB,EAAE,EAAgB,EAAE,KAAiB;QAAjB,sBAAA,EAAA,SAAiB;QAKrF,IAAI,CAAC,KAAK,IAAI,IAAI,IAAI,KAAK,GAAG,CAAC,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,IAAI,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,EAAE;YACrE,OAAO,iBAAM,cAAc,YAAC,SAAS,EAAE,EAAE,EAAE,KAAK,CAAC,CAAC;SACnD;QAGD,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;QAMtB,OAAO,CAAC,CAAC;IACX,CAAC;IACH,kBAAC;AAAD,CAAC,AArCD,CAAoC,WAAW,GAqC9C"}