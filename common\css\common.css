/* 通用样式 */

/* 字体定义 */
@font-face {
    font-family: 'Inter';
    src: url('../../assets/fonts/Inter-Regular.woff2') format('woff2'),
         url('../../assets/fonts/Inter-Regular.woff') format('woff');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Inter';
    src: url('../../assets/fonts/Inter-Bold.woff2') format('woff2'),
         url('../../assets/fonts/Inter-Bold.woff') format('woff');
    font-weight: bold;
    font-style: normal;
    font-display: swap;
}

/* 移动端适配 */
html {
    font-size: 16px;
}

/* 在不同设备上的适配 */
@media screen and (max-width: 375px) {
    html {
        font-size: 14px;
    }
}

@media screen and (min-width: 768px) {
    html {
        font-size: 18px;
    }
}

/* 通用容器 */
.page-container {
    width: 100%;
    max-width: 750px;
    margin: 0 auto;
    overflow: hidden;
    background-color: #FFFFFF;
}

/* 通用卡片样式 */
.card {
    border-radius: 12px;
    background-color: #F8F9FA;
    padding: 16px;
    margin-bottom: 16px;
    box-shadow: 0px 0px 2px 0px rgba(23,26,31,0.07), 0px 0px 4px 0px rgba(23,26,31,0.12);
}

/* 通用按钮样式 */
.btn {
    display: inline-block;
    padding: 8px 16px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: bold;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: #636AE8;
    color: #FFFFFF;
}

.btn-primary:hover {
    background-color: #5258C5;
}

.btn-secondary {
    background-color: #F8F9FA;
    color: #171A1F;
    border: 1px solid #BCC1CA;
}

.btn-secondary:hover {
    background-color: #EBEDF0;
}

/* 通用头像样式 */
.avatar {
    border-radius: 50%;
    overflow: hidden;
}

.avatar-sm {
    width: 40px;
    height: 40px;
}

.avatar-md {
    width: 60px;
    height: 60px;
}

.avatar-lg {
    width: 80px;
    height: 80px;
}

.avatar-xl {
    width: 120px;
    height: 120px;
}

/* 通用文本样式 */
.text-primary {
    color: #636AE8;
}

.text-secondary {
    color: #565E6C;
}

.text-light {
    color: #BCC1CA;
}

.text-dark {
    color: #171A1F;
}

.text-center {
    text-align: center;
}

.text-left {
    text-align: left;
}

.text-right {
    text-align: right;
}

/* 通用边距 */
.mt-1 { margin-top: 4px; }
.mt-2 { margin-top: 8px; }
.mt-3 { margin-top: 16px; }
.mt-4 { margin-top: 24px; }
.mt-5 { margin-top: 32px; }

.mb-1 { margin-bottom: 4px; }
.mb-2 { margin-bottom: 8px; }
.mb-3 { margin-bottom: 16px; }
.mb-4 { margin-bottom: 24px; }
.mb-5 { margin-bottom: 32px; }

.ml-1 { margin-left: 4px; }
.ml-2 { margin-left: 8px; }
.ml-3 { margin-left: 16px; }
.ml-4 { margin-left: 24px; }
.ml-5 { margin-left: 32px; }

.mr-1 { margin-right: 4px; }
.mr-2 { margin-right: 8px; }
.mr-3 { margin-right: 16px; }
.mr-4 { margin-right: 24px; }
.mr-5 { margin-right: 32px; }

/* 通用填充 */
.pt-1 { padding-top: 4px; }
.pt-2 { padding-top: 8px; }
.pt-3 { padding-top: 16px; }
.pt-4 { padding-top: 24px; }
.pt-5 { padding-top: 32px; }

.pb-1 { padding-bottom: 4px; }
.pb-2 { padding-bottom: 8px; }
.pb-3 { padding-bottom: 16px; }
.pb-4 { padding-bottom: 24px; }
.pb-5 { padding-bottom: 32px; }

.pl-1 { padding-left: 4px; }
.pl-2 { padding-left: 8px; }
.pl-3 { padding-left: 16px; }
.pl-4 { padding-left: 24px; }
.pl-5 { padding-left: 32px; }

.pr-1 { padding-right: 4px; }
.pr-2 { padding-right: 8px; }
.pr-3 { padding-right: 16px; }
.pr-4 { padding-right: 24px; }
.pr-5 { padding-right: 32px; }

/* 通用弹性布局 */
.flex {
    display: flex;
}

.flex-column {
    flex-direction: column;
}

.flex-row {
    flex-direction: row;
}

.justify-start {
    justify-content: flex-start;
}

.justify-center {
    justify-content: center;
}

.justify-end {
    justify-content: flex-end;
}

.justify-between {
    justify-content: space-between;
}

.justify-around {
    justify-content: space-around;
}

.align-start {
    align-items: flex-start;
}

.align-center {
    align-items: center;
}

.align-end {
    align-items: flex-end;
}

/* 通用底部导航栏样式 */
.bottom-nav {
    display: flex;
    justify-content: space-around;
    align-items: center;
    width: 100%;
    height: 96px;
    background-color: #FFFFFF;
    border-top: 1px solid #EBEDF0;
    position: fixed;
    bottom: 0;
    left: 0;
    z-index: 100;
}

.nav-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.nav-icon {
    width: 48px;
    height: 48px;
}

.nav-text {
    font-size: 20px;
    color: #565E6C;
    margin-top: 4px;
}

.nav-text.active {
    color: #636AE8;
    font-weight: bold;
}