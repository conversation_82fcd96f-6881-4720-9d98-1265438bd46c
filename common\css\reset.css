/* 重置样式 */

/* 重置所有元素的盒模型 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* 重置HTML和body元素 */
html, body {
    width: 100%;
    height: 100%;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    font-size: 16px;
    line-height: 1.5;
    color: #171A1F;
    background-color: #FFFFFF;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* 重置列表样式 */
ul, ol {
    list-style: none;
}

/* 重置链接样式 */
a {
    text-decoration: none;
    color: inherit;
}

/* 重置图片样式 */
img {
    display: block;
    max-width: 100%;
    height: auto;
    border: none;
}

/* 重置按钮样式 */
button {
    background: none;
    border: none;
    cursor: pointer;
    font-family: inherit;
    font-size: inherit;
    color: inherit;
    padding: 0;
}

/* 重置输入框样式 */
input, textarea, select {
    font-family: inherit;
    font-size: inherit;
    color: inherit;
    border: none;
    outline: none;
}

/* 重置表格样式 */
table {
    border-collapse: collapse;
    border-spacing: 0;
}

/* 清除浮动 */
.clearfix::after {
    content: "";
    display: table;
    clear: both;
}