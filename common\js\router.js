/**
 * 路由管理模块 - 处理页面间的导航和跳转
 */

const Router = {
  /**
   * 当前页面路径
   */
  currentPath: window.location.pathname,

  /**
   * 页面映射表，从site-map.json加载
   */
  pages: {},

  /**
   * 导航关系，从site-map.json加载
   */
  navigation: {},

  /**
   * 初始化路由
   * @returns {Promise} 初始化完成的Promise
   */
  init() {
    return fetch('/site-map.json')
      .then(response => response.json())
      .then(data => {
        this.pages = data.pages.reduce((acc, page) => {
          acc[page.id] = page;
          return acc;
        }, {});
        this.navigation = data.navigation;
        this.setupNavigation();
        return data;
      })
      .catch(error => {
        console.error('加载网站地图失败:', error);
      });
  },

  /**
   * 设置导航事件监听
   */
  setupNavigation() {
    // 底部导航栏
    const navItems = document.querySelectorAll('.nav-item');
    navItems.forEach(item => {
      item.addEventListener('click', (e) => {
        const index = Array.from(navItems).indexOf(item);
        let targetId;
        
        switch(index) {
          case 0: targetId = 'home'; break;
          case 1: targetId = 'essays'; break;
          case 2: targetId = 'growth'; break;
          case 3: targetId = 'personal-center'; break;
          default: return;
        }
        
        this.navigateTo(targetId);
      });
    });

    // 设置项
    const settingItems = document.querySelectorAll('.setting-item');
    settingItems.forEach(item => {
      item.addEventListener('click', () => {
        const text = item.querySelector('.setting-text').textContent.trim();
        let targetId;
        
        switch(text) {
          case '消息中心': targetId = 'message-center'; break;
          case '意见反馈': targetId = 'feedback'; break;
          case '版本信息': targetId = 'version-info'; break;
          case '切换身份': targetId = 'switch-identity'; break;
          case '退出登录': targetId = 'login'; break;
          default: return;
        }
        
        this.navigateTo(targetId);
      });
    });

    // 编辑个人信息
    const profileEdit = document.querySelector('.profile-edit');
    if (profileEdit) {
      profileEdit.addEventListener('click', () => {
        this.navigateTo('edit-profile');
      });
    }

    // 家庭成员
    const familyAvatars = document.querySelectorAll('.family-avatar');
    familyAvatars.forEach(avatar => {
      avatar.addEventListener('click', () => {
        this.navigateTo('family-member');
      });
    });

    // 添加成员
    const addMember = document.querySelector('.add-member');
    if (addMember) {
      addMember.addEventListener('click', () => {
        this.navigateTo('add-family-member');
      });
    }
  },

  /**
   * 导航到指定页面
   * @param {string} pageId 页面ID
   * @param {Object} params 页面参数
   */
  navigateTo(pageId, params = {}) {
    // 当前仅在控制台输出导航信息，实际项目中应该跳转到对应页面
    console.log(`导航到页面: ${pageId}`, params);
    
    if (!this.pages[pageId]) {
      console.warn(`页面 ${pageId} 不存在或尚未实现`);
      return;
    }

    const page = this.pages[pageId];
    const url = new URL(page.path, window.location.origin);
    
    // 添加参数
    Object.keys(params).forEach(key => {
      url.searchParams.append(key, params[key]);
    });
    
    // 在实际项目中取消注释以启用实际导航
    // window.location.href = url.toString();
  },

  /**
   * 获取当前页面ID
   * @returns {string|null} 当前页面ID，如果未找到则返回null
   */
  getCurrentPageId() {
    const path = window.location.pathname;
    for (const id in this.pages) {
      if (this.pages[id].path === path) {
        return id;
      }
    }
    return null;
  },

  /**
   * 获取URL参数
   * @param {string} name 参数名
   * @returns {string|null} 参数值，如果未找到则返回null
   */
  getParam(name) {
    const params = new URLSearchParams(window.location.search);
    return params.get(name);
  }
};

// 导出路由模块
window.Router = Router;